import 'package:flutter/material.dart';

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        appBar: AppBar(
          leading: Icon(Icons.menu, size: 35),
          title: Image.asset('assets/logo.png'),
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 10),
              child: Image.asset('assets/Frame.png'),
            ),
            Padding(
              padding: const EdgeInsets.only(right: 10),
              child: Image.asset('assets/basket.png'),
            ),
          ],
        ),
        body: Container(
          margin: EdgeInsets.only(top: 20),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Container(
                    height: 60,
                    width: 300,
                    padding: EdgeInsets.all(5),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: const Color.fromARGB(255, 183, 183, 183),
                        width: 2,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.search, size: 40),
                        Text('Search here', style: TextStyle(fontSize: 20)),
                      ],
                    ),
                  ),
                  Image.asset('assets/secmenue.png'),
                ],
              ),
              Container(
                margin: EdgeInsets.only(top: 20),
                height: 300,
                width: 450,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.white, Colors.blue.shade100],
                  ),
                ),
                child: Row(
                  children: [
                    Column(
                      children: [
                        Stack(
                          children: [
                            Text(
                              'Todays Deal',
                              style: TextStyle(
                                fontSize: 40,
                                foreground:
                                    Paint()
                                      ..style = PaintingStyle.stroke
                                      ..strokeWidth = 3
                                      ..color = Colors.black,
                              ),
                            ),
                            Text(
                              'Todays Deal',
                              style: TextStyle(
                                fontSize: 40,
                                color: Colors.white, // لون التعبئة
                              ),
                            ),
                          ],
                        ),

                        Text(
                          '100% OFF',
                          style: TextStyle(
                            fontSize: 60,
                            fontWeight: FontWeight.bold,
                          ),
                        ),

                        Text(''' 
 Et provident eos est dolore. Eum libero
 eligendi molestias aut et quibusdam
  aspernatur.
''', style: TextStyle(fontSize: 15)),
                        Container(
                          height: 60,
                          width: 150,
                          child: Image.asset(
                            'assets/button.png',
                            fit: BoxFit.contain,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      height: 300,
                      width: 150,
                      child: Image.asset('assets/wom.png', fit: BoxFit.cover),
                    ),
                  ],
                ),
              ),
              Container(
                height: 100,
                padding: EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade100, Colors.white],
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Top Rated Freelances',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'View All',
                      style: TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.bold,
                        color: const Color.fromARGB(255, 3, 86, 154),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
