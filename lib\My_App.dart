import 'package:flutter/material.dart';

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        appBar: AppBar(
          leading: Icon(Icons.menu, size: 35),
          title: Image.asset('assets/logo.png'),
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 10),
              child: Image.asset('assets/Frame.png'),
            ),
            Padding(
              padding: const EdgeInsets.only(right: 10),
              child: Image.asset('assets/basket.png'),
            ),
          ],
        ),
        body: Container(
          margin: EdgeInsets.only(top: 20),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Container(
                    height: 60,
                    width: 300,
                    padding: EdgeInsets.all(5),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: const Color.fromARGB(255, 183, 183, 183),
                        width: 2,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.search, size: 40),
                        Text('Search here', style: TextStyle(fontSize: 20)),
                      ],
                    ),
                  ),
                  Image.asset('assets/secmenue.png'),
                ],
              ),
              Container(
                margin: EdgeInsets.only(top: 20),
                height: 300,
                width: 450,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.white, Colors.blue.shade100],
                  ),
                ),
                child: Row(
                  children: [
                    Column(
                      children: [
                        Stack(
                          children: [
                            Text(
                              'Todays Deal',
                              style: TextStyle(
                                fontSize: 40,
                                foreground:
                                    Paint()
                                      ..style = PaintingStyle.stroke
                                      ..strokeWidth = 3
                                      ..color = Colors.black,
                              ),
                            ),
                            Text(
                              'Todays Deal',
                              style: TextStyle(
                                fontSize: 40,
                                color: Colors.white, // لون التعبئة
                              ),
                            ),
                          ],
                        ),

                        Text(
                          '100% OFF',
                          style: TextStyle(
                            fontSize: 60,
                            fontWeight: FontWeight.bold,
                          ),
                        ),

                        Text(''' 
 Et provident eos est dolore. Eum libero
 eligendi molestias aut et quibusdam
  aspernatur.
''', style: TextStyle(fontSize: 15)),
                        Container(
                          height: 60,
                          width: 150,
                          child: Image.asset(
                            'assets/button.png',
                            fit: BoxFit.contain,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      height: 300,
                      width: 150,
                      child: Image.asset('assets/wom.png', fit: BoxFit.cover),
                    ),
                  ],
                ),
              ),
              Container(
                height: 100,
                padding: EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade100, Colors.white],
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Top Rated Freelances',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'View All',
                      style: TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.bold,
                        color: const Color.fromARGB(255, 3, 86, 154),
                      ),
                    ),
                  ],
                ),
              ),
              // Button للرسائل المهمة
              Container(
                margin: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                child: ElevatedButton(
                  onPressed: () {
                    // عرض الرسائل المهمة
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: Row(
                            children: [
                              Icon(Icons.priority_high, color: Colors.red, size: 30),
                              SizedBox(width: 10),
                              Text('الرسائل المهمة'),
                            ],
                          ),
                          content: SizedBox(
                            height: 200,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildImportantMessage(
                                  'تحديث النظام',
                                  'يرجى تحديث التطبيق للحصول على أحدث الميزات',
                                  Icons.system_update,
                                  Colors.blue,
                                ),
                                SizedBox(height: 10),
                                _buildImportantMessage(
                                  'عرض خاص',
                                  'خصم 50% على جميع الخدمات لفترة محدودة',
                                  Icons.local_offer,
                                  Colors.green,
                                ),
                                SizedBox(height: 10),
                                _buildImportantMessage(
                                  'تنبيه أمني',
                                  'تم تسجيل دخول جديد من جهاز غير معروف',
                                  Icons.security,
                                  Colors.orange,
                                ),
                              ],
                            ),
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: Text('إغلاق'),
                            ),
                          ],
                        );
                      },
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade600,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    elevation: 5,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.notification_important, size: 24),
                      SizedBox(width: 10),
                      Text(
                        'الرسائل المهمة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(width: 10),
                      Container(
                        padding: EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: Text(
                          '3',
                          style: TextStyle(
                            color: Colors.red.shade600,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // دالة مساعدة لبناء عنصر الرسالة المهمة
  Widget _buildImportantMessage(String title, String message, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: color,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  message,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
